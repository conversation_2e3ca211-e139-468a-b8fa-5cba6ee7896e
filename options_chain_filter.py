"""
Options Chain Filter for Multi-Market Type Scanner.
Handles filtering of options symbols to create option chains around ATM strikes.
"""

import logging
from typing import Dict, List, Optional, Set, Tuple
from dataclasses import dataclass
from collections import defaultdict
from universal_symbol_parser import UniversalSymbol
from config_loader import ConfigLoader

logger = logging.getLogger(__name__)

@dataclass
class OptionsChainData:
    """Data structure for options chain information."""
    underlying: str
    expiry_year: str
    expiry_month: str
    spot_price: Optional[float] = None
    atm_strike: Optional[float] = None
    strike_multiplier: Optional[float] = None
    ce_options: List[UniversalSymbol] = None
    pe_options: List[UniversalSymbol] = None
    
    def __post_init__(self):
        if self.ce_options is None:
            self.ce_options = []
        if self.pe_options is None:
            self.pe_options = []


class OptionsChainFilter:
    """Filter for creating options chains around ATM strikes."""
    
    def __init__(self, config: ConfigLoader):
        """
        Initialize the options chain filter.
        
        Args:
            config: Configuration loader instance
        """
        self.config = config
        self.strike_level = config.options_strike_level
        
        logger.info(f"Options chain filter initialized with strike level: {self.strike_level}")
    
    def detect_strike_multiplier(self, options: List[UniversalSymbol]) -> float:
        """
        Detect the strike price multiplier for the given options.
        
        Args:
            options: List of options symbols
            
        Returns:
            Detected multiplier (e.g., 50, 100, 250, 500)
        """
        if not options:
            return 50.0  # Default multiplier
        
        # Collect all strike prices
        strikes = [opt.strike_price for opt in options if opt.strike_price is not None]
        
        if len(strikes) < 2:
            return 50.0
        
        # Sort strikes to find differences
        strikes.sort()
        
        # Calculate differences between consecutive strikes
        differences = []
        for i in range(1, len(strikes)):
            diff = strikes[i] - strikes[i-1]
            if diff > 0:
                differences.append(diff)
        
        if not differences:
            return 50.0
        
        # Find the most common difference (mode)
        diff_counts = {}
        for diff in differences:
            diff_counts[diff] = diff_counts.get(diff, 0) + 1
        
        # Get the most frequent difference
        most_common_diff = max(diff_counts.keys(), key=lambda x: diff_counts[x])
        
        logger.debug(f"Detected strike multiplier: {most_common_diff}")
        return most_common_diff
    
    def estimate_atm_strike(self, options: List[UniversalSymbol], 
                           spot_price: Optional[float] = None) -> Optional[float]:
        """
        Estimate the ATM (At The Money) strike price.
        
        Args:
            options: List of options symbols
            spot_price: Current spot price (if available)
            
        Returns:
            Estimated ATM strike price
        """
        if not options:
            return None
        
        # If we have spot price, use it to find nearest strike
        if spot_price is not None:
            strikes = [opt.strike_price for opt in options if opt.strike_price is not None]
            if strikes:
                # Find the strike closest to spot price
                closest_strike = min(strikes, key=lambda x: abs(x - spot_price))
                logger.debug(f"ATM strike based on spot price {spot_price}: {closest_strike}")
                return closest_strike
        
        # Otherwise, use the middle strike as approximation
        strikes = sorted([opt.strike_price for opt in options if opt.strike_price is not None])
        if strikes:
            middle_index = len(strikes) // 2
            atm_strike = strikes[middle_index]
            logger.debug(f"ATM strike estimated from middle: {atm_strike}")
            return atm_strike
        
        return None
    
    def group_options_by_expiry(self, options: List[UniversalSymbol]) -> Dict[Tuple[str, str], List[UniversalSymbol]]:
        """
        Group options by expiry (year + month).
        
        Args:
            options: List of options symbols
            
        Returns:
            Dictionary mapping (year, month) to list of options
        """
        grouped = defaultdict(list)
        
        for option in options:
            if option.is_options() and option.expiry_year and option.expiry_month:
                key = (option.expiry_year, option.expiry_month)
                grouped[key].append(option)
        
        return dict(grouped)
    
    def filter_options_around_atm(self, options: List[UniversalSymbol], 
                                 atm_strike: float, 
                                 strike_multiplier: float) -> Tuple[List[UniversalSymbol], List[UniversalSymbol]]:
        """
        Filter options to get CE and PE options around ATM strike.
        
        Args:
            options: List of options symbols
            atm_strike: ATM strike price
            strike_multiplier: Strike price multiplier
            
        Returns:
            Tuple of (CE options, PE options) within strike level range
        """
        # Calculate strike range
        strike_range = self.strike_level * strike_multiplier
        min_strike = atm_strike - strike_range
        max_strike = atm_strike + strike_range
        
        ce_options = []
        pe_options = []
        
        for option in options:
            if (option.strike_price is not None and 
                min_strike <= option.strike_price <= max_strike):
                
                if option.option_type == 'CE':
                    ce_options.append(option)
                elif option.option_type == 'PE':
                    pe_options.append(option)
        
        # Sort by strike price
        ce_options.sort(key=lambda x: x.strike_price)
        pe_options.sort(key=lambda x: x.strike_price)
        
        logger.debug(f"Filtered options around ATM {atm_strike}: {len(ce_options)} CE, {len(pe_options)} PE")
        return ce_options, pe_options
    
    def create_options_chain(self, underlying: str, options: List[UniversalSymbol], 
                           spot_price: Optional[float] = None) -> List[OptionsChainData]:
        """
        Create options chains for the given underlying and options.
        
        Args:
            underlying: Underlying symbol
            options: List of options symbols for this underlying
            spot_price: Current spot price (if available)
            
        Returns:
            List of OptionsChainData objects
        """
        chains = []
        
        # Group options by expiry
        expiry_groups = self.group_options_by_expiry(options)
        
        for (year, month), expiry_options in expiry_groups.items():
            try:
                # Detect strike multiplier for this expiry
                strike_multiplier = self.detect_strike_multiplier(expiry_options)
                
                # Estimate ATM strike
                atm_strike = self.estimate_atm_strike(expiry_options, spot_price)
                
                if atm_strike is None:
                    logger.warning(f"Could not determine ATM strike for {underlying} {year}{month}")
                    continue
                
                # Filter options around ATM
                ce_options, pe_options = self.filter_options_around_atm(
                    expiry_options, atm_strike, strike_multiplier
                )
                
                # Only create chain if we have both CE and PE options
                if ce_options and pe_options:
                    chain = OptionsChainData(
                        underlying=underlying,
                        expiry_year=year,
                        expiry_month=month,
                        spot_price=spot_price,
                        atm_strike=atm_strike,
                        strike_multiplier=strike_multiplier,
                        ce_options=ce_options,
                        pe_options=pe_options
                    )
                    chains.append(chain)
                    
                    logger.debug(f"Created options chain for {underlying} {year}{month}: "
                               f"{len(ce_options)} CE, {len(pe_options)} PE options")
                else:
                    logger.debug(f"Skipping {underlying} {year}{month}: insufficient CE/PE pairs")
                    
            except Exception as e:
                logger.error(f"Error creating options chain for {underlying} {year}{month}: {e}")
                continue
        
        logger.info(f"Created {len(chains)} options chains for {underlying}")
        return chains
    
    def filter_options_symbols(self, options: List[UniversalSymbol], 
                             spot_prices: Optional[Dict[str, float]] = None) -> List[UniversalSymbol]:
        """
        Filter options symbols to create option chains around ATM strikes.
        
        Args:
            options: List of options symbols
            spot_prices: Optional dictionary of underlying -> spot price
            
        Returns:
            Filtered list of options symbols that form valid chains
        """
        if not options:
            return []
        
        # Group options by underlying
        underlying_groups = defaultdict(list)
        for option in options:
            if option.is_options():
                underlying_groups[option.underlying].append(option)
        
        filtered_options = []
        
        for underlying, underlying_options in underlying_groups.items():
            try:
                # Get spot price if available
                spot_price = spot_prices.get(underlying) if spot_prices else None
                
                # Create options chains
                chains = self.create_options_chain(underlying, underlying_options, spot_price)
                
                # Collect all options from valid chains
                for chain in chains:
                    filtered_options.extend(chain.ce_options)
                    filtered_options.extend(chain.pe_options)
                    
            except Exception as e:
                logger.error(f"Error filtering options for {underlying}: {e}")
                continue
        
        logger.info(f"Options chain filter: {len(filtered_options)}/{len(options)} options selected")
        return filtered_options
    
    def get_options_summary(self, chains: List[OptionsChainData]) -> Dict[str, any]:
        """
        Get summary statistics for options chains.
        
        Args:
            chains: List of options chains
            
        Returns:
            Summary statistics dictionary
        """
        if not chains:
            return {}
        
        total_ce = sum(len(chain.ce_options) for chain in chains)
        total_pe = sum(len(chain.pe_options) for chain in chains)
        unique_underlyings = len(set(chain.underlying for chain in chains))
        
        return {
            'total_chains': len(chains),
            'unique_underlyings': unique_underlyings,
            'total_ce_options': total_ce,
            'total_pe_options': total_pe,
            'total_options': total_ce + total_pe
        }
