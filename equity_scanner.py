"""
Equity scanner for filtering equity symbols based on criteria.
Similar to IndexScanner but designed for equity (cash market) symbols.
"""

import logging
import subprocess
from typing import List, Dict, Tuple
from dataclasses import dataclass
import re
import csv
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

from config_loader import ConfigLoader
from equity_symbol_parser import EquitySymbolParser
from fyers_client import FyersClient, MarketData
from technical_indicators import MAEAnalyzer

logger = logging.getLogger(__name__)

@dataclass
class FilteredEquitySymbol:
    """Data class for filtered equity symbol with market data."""
    symbol: str
    underlying: str
    ltp: float
    volume: int
    open_price: float
    high: float
    low: float
    close: float
    prev_close: float
    change: float
    change_percent: float
    ema_short: float = None
    ema_long: float = None
    ema_signal: str = "NEUTRAL"

class EquityScanner:
    """Main equity scanner for filtering equity symbols based on criteria."""
    
    def __init__(self, config: ConfigLoader):
        """
        Initialize the equity scanner.
        
        Args:
            config: Configuration loader instance
        """
        self.config = config
        self.symbol_parser = EquitySymbolParser(target_symbols=config.symbols)
        self.fyers_client = FyersClient(env_path=config.env_path)

        # Initialize MAE analyzer if enabled
        if config.mae_enabled:
            self.mae_analyzer = MAEAnalyzer(
                length=config.mae_length,
                source=config.mae_source,
                offset=config.mae_offset,
                smoothing_line=config.mae_smoothing_line,
                smoothing_length=config.mae_smoothing_length
            )
        else:
            self.mae_analyzer = None
        
    def authenticate_fyers(self) -> bool:
        """
        Authenticate with Fyers API.
        
        Returns:
            True if authentication successful, False otherwise
        """
        logger.info("Authenticating with Fyers API for equity scanning...")
        return self.fyers_client.authenticate()

    def extract_underlying_from_symbol(self, symbol_str: str) -> str:
        """
        Extract underlying symbol from equity symbol string.

        Args:
            symbol_str: Symbol string like 'NSE:ABAN-EQ'

        Returns:
            Underlying symbol ('ABAN', 'RELIANCE', etc.)
        """
        try:
            # Remove NSE: prefix if present
            clean_symbol = symbol_str.replace('NSE:', '')

            # Extract underlying by removing -EQ suffix
            if clean_symbol.endswith('-EQ'):
                return clean_symbol[:-3]  # Remove last 3 characters (-EQ)
            else:
                return "UNKNOWN"

        except Exception as e:
            logger.debug(f"Error extracting underlying from {symbol_str}: {e}")
            return "UNKNOWN"

    def apply_volume_filter(self, market_data: Dict[str, MarketData]) -> Dict[str, MarketData]:
        """
        Apply volume filter to market data.
        
        Args:
            market_data: Dictionary of symbol -> MarketData
            
        Returns:
            Filtered dictionary of market data
        """
        filtered_data = {}
        
        for symbol, data in market_data.items():
            try:
                volume = getattr(data, 'volume', 0)
                
                if self.config.min_volume <= volume <= self.config.max_volume:
                    filtered_data[symbol] = data
                else:
                    logger.debug(f"Symbol {symbol} filtered out by volume: {volume}")
                    
            except Exception as e:
                logger.debug(f"Error processing volume filter for {symbol}: {e}")
                continue
                
        logger.info(f"Volume filter: {len(filtered_data)}/{len(market_data)} symbols passed")
        return filtered_data

    def apply_ltp_filter(self, market_data: Dict[str, MarketData]) -> Dict[str, MarketData]:
        """
        Apply LTP (Last Traded Price) filter to market data.
        
        Args:
            market_data: Dictionary of symbol -> MarketData
            
        Returns:
            Filtered dictionary of market data
        """
        filtered_data = {}
        
        for symbol, data in market_data.items():
            try:
                ltp = getattr(data, 'ltp', 0.0)
                
                if self.config.min_ltp_price <= ltp <= self.config.max_ltp_price:
                    filtered_data[symbol] = data
                else:
                    logger.debug(f"Symbol {symbol} filtered out by LTP: {ltp}")
                    
            except Exception as e:
                logger.debug(f"Error processing LTP filter for {symbol}: {e}")
                continue
                
        logger.info(f"LTP filter: {len(filtered_data)}/{len(market_data)} symbols passed")
        return filtered_data

    def apply_mae_filter(self, market_data: Dict[str, MarketData]) -> Dict[str, MarketData]:
        """
        Apply MAE (Moving Average Exponential) filter if enabled.
        
        Args:
            market_data: Dictionary of symbol -> MarketData
            
        Returns:
            Filtered dictionary of market data with MAE analysis
        """
        if not self.config.mae_enabled or not self.mae_analyzer:
            logger.info("MAE filter disabled, skipping...")
            return market_data

        logger.info("Applying MAE filter to equity symbols...")
        filtered_data = {}
        
        for symbol, data in market_data.items():
            try:
                # Get historical data for MAE calculation
                historical_data = self.fyers_client.get_historical_data(
                    symbol=symbol,
                    interval=self.config.interval,
                    days=self.config.days_to_fetch
                )
                
                if historical_data:
                    # Calculate MAE
                    mae_result = self.mae_analyzer.calculate_mae(historical_data)
                    
                    if mae_result and mae_result.get('signal') != 'NEUTRAL':
                        # Add MAE data to market data
                        data.ema_short = mae_result.get('ema_short')
                        data.ema_long = mae_result.get('ema_long')
                        data.ema_signal = mae_result.get('signal', 'NEUTRAL')
                        filtered_data[symbol] = data
                    else:
                        logger.debug(f"Symbol {symbol} filtered out by MAE: neutral signal")
                else:
                    logger.debug(f"No historical data available for {symbol}")
                    
            except Exception as e:
                logger.debug(f"Error processing MAE filter for {symbol}: {e}")
                continue
                
        logger.info(f"MAE filter: {len(filtered_data)}/{len(market_data)} symbols passed")
        return filtered_data

    def apply_filters(self, market_data: Dict[str, MarketData]) -> List[FilteredEquitySymbol]:
        """
        Apply all configured filters to market data.
        
        Args:
            market_data: Dictionary of symbol -> MarketData
            
        Returns:
            List of FilteredEquitySymbol objects that pass all filters
        """
        logger.info(f"Applying filters to {len(market_data)} equity symbols...")
        
        # Apply volume filter
        filtered_data = self.apply_volume_filter(market_data)
        
        # Apply LTP filter
        filtered_data = self.apply_ltp_filter(filtered_data)
        
        # Apply MAE filter if enabled
        filtered_data = self.apply_mae_filter(filtered_data)
        
        # Convert to FilteredEquitySymbol objects
        filtered_symbols = []
        
        for symbol, data in filtered_data.items():
            try:
                underlying = self.extract_underlying_from_symbol(symbol)
                
                filtered_symbol = FilteredEquitySymbol(
                    symbol=symbol,
                    underlying=underlying,
                    ltp=getattr(data, 'ltp', 0.0),
                    volume=getattr(data, 'volume', 0),
                    open_price=getattr(data, 'open_price', 0.0),
                    high=getattr(data, 'high', 0.0),
                    low=getattr(data, 'low', 0.0),
                    close=getattr(data, 'close', 0.0),
                    prev_close=getattr(data, 'prev_close', 0.0),
                    change=getattr(data, 'change', 0.0),
                    change_percent=getattr(data, 'change_percent', 0.0),
                    ema_short=getattr(data, 'ema_short', None),
                    ema_long=getattr(data, 'ema_long', None),
                    ema_signal=getattr(data, 'ema_signal', 'NEUTRAL')
                )
                
                filtered_symbols.append(filtered_symbol)
                
            except Exception as e:
                logger.debug(f"Error creating FilteredEquitySymbol for {symbol}: {e}")
                continue
        
        logger.info(f"Final result: {len(filtered_symbols)} equity symbols passed all filters")
        return filtered_symbols

    def scan_symbols(self) -> List[FilteredEquitySymbol]:
        """
        Main scanning method that orchestrates the entire equity scanning process.
        
        Returns:
            List of FilteredEquitySymbol objects that pass all filters
        """
        try:
            logger.info("Starting equity scanner...")
            
            # Step 1: Authenticate with Fyers
            if not self.authenticate_fyers():
                logger.error("Failed to authenticate with Fyers API")
                return []
            
            # Step 2: Get symbols for scanning
            logger.info("Loading equity symbols from NSE_CM.csv...")
            # When using 'ALL', pass None to use all target symbols from parser
            underlying_filter = None if 'ALL' in self.config.symbols else self.config.symbols
            symbols_to_scan = self.symbol_parser.get_symbols_for_scanning(
                underlying_symbols=underlying_filter
            )

            if not symbols_to_scan:
                logger.warning("No equity symbols found for scanning")
                return []

            # Save filtered symbols to CSV to avoid rate limiting
            symbol_objects = self.symbol_parser.get_symbol_details(underlying_symbols=underlying_filter)
            self.symbol_parser.save_filtered_symbols_to_csv(symbol_objects, "filtered_equity_symbols.csv")

            logger.info(f"Found {len(symbols_to_scan)} equity symbols to scan")
            
            # Step 3: Fetch market data
            logger.info("Fetching market data from Fyers API for equity symbols...")
            market_data = self.fyers_client.get_quotes(symbols_to_scan)
            
            if not market_data:
                logger.warning("No market data received for equity symbols")
                return []
            
            logger.info(f"Received market data for {len(market_data)} equity symbols")

            # Write all market data to CSV file
            with open('all_equity_market_data.csv', 'w', newline='') as csvfile:
                # Conditionally add mae_value column to the CSV only if MAE indicator is enabled.
                if self.config.mae_enabled:
                    fieldnames = ['symbol', 'ltp', 'volume', 'open_price', 'high', 'low', 'close', 'prev_close', 'change', 'change_percent', 'mae_value']
                else:
                    fieldnames = ['symbol', 'ltp', 'volume', 'open_price', 'high', 'low', 'close', 'prev_close', 'change', 'change_percent']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                for symbol, data in market_data.items():
                    row = {
                        'symbol': symbol,
                        'ltp': getattr(data, 'ltp', ''),
                        'volume': getattr(data, 'volume', ''),
                        'open_price': getattr(data, 'open_price', ''),
                        'high': getattr(data, 'high', ''),
                        'low': getattr(data, 'low', ''),
                        'close': getattr(data, 'close', ''),
                        'prev_close': getattr(data, 'prev_close', ''),
                        'change': getattr(data, 'change', ''),
                        'change_percent': getattr(data, 'change_percent', '')
                    }
                    if self.config.mae_enabled:
                        # Try to get the MAE value from the correct attribute
                        if hasattr(data, 'mae_value'):
                            row['mae_value'] = getattr(data, 'mae_value', '')
                        elif hasattr(data, 'ema_short'):
                            row['mae_value'] = getattr(data, 'ema_short', '')
                        else:
                            row['mae_value'] = ''
                    writer.writerow(row)

            # Step 4: Apply filters
            logger.info("Applying filters to equity symbols...")
            filtered_symbols = self.apply_filters(market_data)
            
            logger.info(f"Equity scanning complete. Found {len(filtered_symbols)} symbols matching criteria")
            return filtered_symbols
            
        except Exception as e:
            logger.error(f"Error during equity scanning: {e}")
            return []
    
    def get_scan_summary(self, filtered_symbols: List[FilteredEquitySymbol]) -> Dict[str, int]:
        """
        Generate summary statistics for the scan results.
        
        Args:
            filtered_symbols: List of filtered equity symbols
            
        Returns:
            Dictionary with summary statistics
        """
        if not filtered_symbols:
            return {
                'total_symbols': 0,
                'unique_underlyings': 0,
                'avg_volume': 0,
                'avg_ltp': 0.0
            }
        
        # Calculate statistics
        total_symbols = len(filtered_symbols)
        unique_underlyings = len(set(symbol.underlying for symbol in filtered_symbols))
        
        total_volume = sum(symbol.volume for symbol in filtered_symbols)
        avg_volume = total_volume // total_symbols if total_symbols > 0 else 0
        
        total_ltp = sum(symbol.ltp for symbol in filtered_symbols)
        avg_ltp = total_ltp / total_symbols if total_symbols > 0 else 0.0
        
        # Count by underlying
        underlying_counts = {}
        for symbol in filtered_symbols:
            underlying = symbol.underlying
            underlying_counts[underlying] = underlying_counts.get(underlying, 0) + 1
        
        summary = {
            'total_symbols': total_symbols,
            'unique_underlyings': unique_underlyings,
            'avg_volume': avg_volume,
            'avg_ltp': round(avg_ltp, 2),
            'underlying_counts': underlying_counts
        }
        
        logger.info(f"Equity scan summary: {summary}")
        return summary