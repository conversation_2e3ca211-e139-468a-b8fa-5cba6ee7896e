import logging
import subprocess
from typing import List, Dict, Tuple
from dataclasses import dataclass
import re
import csv
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, as_completed

from config_loader import ConfigLoader
from symbol_parser import SymbolParser
from fyers_client import FyersClient, MarketData
from technical_indicators import MAEAnalyzer

logger = logging.getLogger(__name__)

@dataclass
class FilteredSymbol:
    """Data class for filtered symbol with market data."""
    strike: float
    expiry_date: str
    option_type: str  # CE or PE
    symbol: str
    ltp: float
    volume: int
    open_price: float
    high: float
    low: float
    close: float
    prev_close: float
    change: float
    change_percent: float
    ema_short: float = None
    ema_long: float = None
    ema_signal: str = "NEUTRAL"
    pair_id: str = None  # Unique identifier for CE/PE pairs
    pair_partner: str = None  # Symbol of the paired option

class IndexScanner:
    """Main index scanner for filtering options based on criteria."""
    
    def __init__(self, config: ConfigLoader):
        """
        Initialize the index scanner.
        
        Args:
            config: Configuration loader instance
        """
        self.config = config
        self.symbol_parser = SymbolParser(target_symbols=config.symbols)
        self.fyers_client = FyersClient(env_path=config.env_path)

        # Initialize MAE analyzer if enabled
        if config.mae_enabled:
            self.mae_analyzer = MAEAnalyzer(
                length=config.mae_length,
                source=config.mae_source,
                offset=config.mae_offset,
                smoothing_line=config.mae_smoothing_line,
                smoothing_length=config.mae_smoothing_length
            )
        else:
            self.mae_analyzer = None
        
    def authenticate_fyers(self) -> bool:
        """
        Authenticate with Fyers API.
        
        Returns:
            True if authentication successful, False otherwise
        """
        logger.info("Authenticating with Fyers API...")
        return self.fyers_client.authenticate()
    
    def extract_expiry_info(self, symbol_str: str) -> Tuple[str, str]:
        """
        Extract expiry date and format it for display.
        
        Args:
            symbol_str: Symbol string like 'NSE:NIFTY25JUL57000CE'
            
        Returns:
            Tuple of (formatted_expiry_date, option_type)
        """
        try:
            # Remove NSE: prefix
            clean_symbol = symbol_str.replace('NSE:', '')
            
            # Extract components using regex
            pattern = r'^([A-Z&]+)(\d{2})([A-Z]{3})(\d+(?:\.\d+)?)(CE|PE)$'
            match = re.match(pattern, clean_symbol)
            
            if match:
                year = match.group(2)
                month = match.group(3)
                option_type = match.group(5)
                
                # Format as readable date
                expiry_date = f"20{year}-{month}"
                return expiry_date, option_type
            else:
                return "Unknown", "Unknown"
                
        except Exception as e:
            logger.debug(f"Error extracting expiry info from {symbol_str}: {e}")
            return "Unknown", "Unknown"
    
    def extract_strike_price(self, symbol_str: str) -> float:
        """
        Extract strike price from symbol string.

        Args:
            symbol_str: Symbol string like 'NSE:NIFTY25JUL57000CE'

        Returns:
            Strike price as float
        """
        try:
            # Remove NSE: prefix
            clean_symbol = symbol_str.replace('NSE:', '')

            # Extract strike price using regex
            pattern = r'^([A-Z&]+)(\d{2})([A-Z]{3})(\d+(?:\.\d+)?)(CE|PE)$'
            match = re.match(pattern, clean_symbol)

            if match:
                return float(match.group(4))
            else:
                return 0.0

        except Exception as e:
            logger.debug(f"Error extracting strike price from {symbol_str}: {e}")
            return 0.0

    def extract_underlying_from_symbol(self, symbol_str: str) -> str:
        """
        Extract underlying symbol (NIFTY/BANKNIFTY) from symbol string.

        Args:
            symbol_str: Symbol string like 'NSE:NIFTY25JUL57000CE'

        Returns:
            Underlying symbol ('NIFTY' or 'BANKNIFTY')
        """
        try:
            # Remove NSE: prefix if present
            clean_symbol = symbol_str.replace('NSE:', '')

            # Extract underlying using regex
            pattern = r'^([A-Z&]+)'
            match = re.match(pattern, clean_symbol)

            if match:
                return match.group(1)
            else:
                return "UNKNOWN"

        except Exception as e:
            logger.debug(f"Error extracting underlying from {symbol_str}: {e}")
            return "UNKNOWN"

    def extract_month_from_symbol(self, symbol_str: str) -> str:
        """
        Extract month from symbol string.

        Args:
            symbol_str: Symbol string like 'NSE:NIFTY25JUL57000CE'

        Returns:
            Month abbreviation like 'JUL'
        """
        try:
            # Remove NSE: prefix if present
            clean_symbol = symbol_str.replace('NSE:', '')

            # Extract month using regex
            pattern = r'^([A-Z&]+)(\d{2})([A-Z]{3})'
            match = re.match(pattern, clean_symbol)

            if match:
                return match.group(3)
            else:
                return "UNKNOWN"

        except Exception as e:
            logger.debug(f"Error extracting month from {symbol_str}: {e}")
            return "UNKNOWN"
    
    def extract_base_symbol(self, symbol_str: str) -> str:
        """
        Extract base symbol (underlying + year + month) from symbol string.
        This is used for CE/PE pairing - symbols with the same base can be paired.

        Args:
            symbol_str: Symbol string like 'NSE:NIFTY25JUL27000CE'

        Returns:
            Base symbol like 'NIFTY25JUL'
        """
        try:
            # Remove NSE: prefix if present
            clean_symbol = symbol_str.replace('NSE:', '')

            # Extract base using regex (underlying + year + month)
            pattern = r'^([A-Z&]+)(\d{2})([A-Z]{3})'
            match = re.match(pattern, clean_symbol)

            if match:
                underlying = match.group(1)
                year = match.group(2)
                month = match.group(3)
                return f"{underlying}{year}{month}"
            else:
                return "UNKNOWN"

        except Exception as e:
            logger.debug(f"Error extracting base symbol from {symbol_str}: {e}")
            return "UNKNOWN"

    def extract_month_from_symbol(self, symbol_str: str) -> str:
        """
        Extract month from symbol string.

        Args:
            symbol_str: Symbol string like 'NSE:NIFTY25JUL23000CE'

        Returns:
            Month abbreviation like 'JUL'
        """
        try:
            # Remove NSE: prefix if present
            clean_symbol = symbol_str.replace('NSE:', '')

            # Extract month using regex (underlying + year + month)
            pattern = r'^([A-Z&]+)(\d{2})([A-Z]{3})'
            match = re.match(pattern, clean_symbol)

            if match:
                return match.group(3)
            else:
                return "UNKNOWN"

        except Exception as e:
            logger.debug(f"Error extracting month from {symbol_str}: {e}")
            return "UNKNOWN"
    
    def apply_ce_pe_pairing_filter(self, filtered_symbols: List[FilteredSymbol]) -> List[FilteredSymbol]:
        """
        Apply CE/PE pairing filter to retain only symbols that have both CE and PE pairs
        with price percentage filtering and same expiry month requirement.
        Groups symbols by base (underlying + year + month) and keeps only those groups
        that contain at least one CE and one PE option within the specified price range.

        Args:
            filtered_symbols: List of FilteredSymbol objects after LTP filtering

        Returns:
            List of FilteredSymbol objects that have valid CE/PE pairs within price range
        """
        if not self.config.ce_pe_pairing_enabled:
            logger.info("CE/PE pairing filter is disabled")
            return filtered_symbols

        min_price_percent = self.config.ce_pe_min_price_percent
        max_price_percent = self.config.ce_pe_max_price_percent

        logger.info(f"Applying CE/PE pairing filter with price range: {min_price_percent}% - {max_price_percent}%")

        # Performance optimization: Group symbols by base symbol using optimized parsing
        base_groups = {}
        base_pattern = re.compile(r'^NSE:([A-Z&]+)(\d{2})([A-Z]{3})')  # Pre-compiled regex

        for symbol in filtered_symbols:
            # Optimized base symbol extraction
            match = base_pattern.match(symbol.symbol)
            if not match:
                logger.debug(f"Could not extract base symbol from {symbol.symbol}, skipping")
                continue

            base = f"{match.group(1)}{match.group(2)}{match.group(3)}"

            # Quick validation: option_type should already be validated during filtering
            if symbol.option_type not in ['CE', 'PE']:
                logger.debug(f"Unknown option type {symbol.option_type} for symbol {symbol.symbol}")
                continue

            # Initialize base group if needed
            if base not in base_groups:
                base_groups[base] = {'CE': [], 'PE': []}

            base_groups[base][symbol.option_type].append(symbol)

        # Create CE/PE pairs with price filtering
        paired_symbols = []
        valid_pairs = 0
        discarded_groups = 0
        price_filtered_count = 0

        # pairing_counter removed
        for base, group in base_groups.items():
            ce_options = group['CE']
            pe_options = group['PE']

            if len(ce_options) > 0 and len(pe_options) > 0:
                # Find valid CE/PE pairs within price range with 1:1 matching
                valid_pairs_for_group = []
                used_pe_options = set()

                # Sort CE options by LTP for consistent pairing
                sorted_ce_options = sorted(ce_options, key=lambda x: x.ltp)

                for ce_option in sorted_ce_options:
                    ce_price = ce_option.ltp
                    best_pe_match = None
                    best_price_diff = float('inf')

                    # Find the best available PE match
                    for pe_option in pe_options:
                        if pe_option.symbol in used_pe_options:
                            continue  # Skip already used PE options

                        pe_price = pe_option.ltp

                        # Calculate price difference percentage
                        if ce_price > 0 and pe_price > 0:
                            # Use average of both prices as base for percentage calculation
                            average_price = (ce_price + pe_price) / 2
                            price_diff_percent = abs(ce_price - pe_price) / average_price * 100

                            if min_price_percent <= price_diff_percent <= max_price_percent:
                                if price_diff_percent < best_price_diff:
                                    best_price_diff = price_diff_percent
                                    best_pe_match = pe_option

                    # If we found a valid PE match, create the pair
                    if best_pe_match:
                        # Generate unique pair ID
                        pair_id = f"{base}_{len(valid_pairs_for_group)+1}"

                        # Set pair information for both options
                        ce_option.pair_id = pair_id
                        ce_option.pair_partner = best_pe_match.symbol
                        best_pe_match.pair_id = pair_id
                        best_pe_match.pair_partner = ce_option.symbol

                        pair = (ce_option, best_pe_match)
                        valid_pairs_for_group.append(pair)
                        used_pe_options.add(best_pe_match.symbol)
                        logger.info(f"Paired {ce_option.symbol} (LTP: {ce_price}) with {best_pe_match.symbol} (LTP: {best_pe_match.ltp}) - {best_price_diff:.2f}% difference")

                # Only add pairs if we have valid 1:1 CE/PE matches
                if valid_pairs_for_group:
                    for ce_option, pe_option in valid_pairs_for_group:
                        paired_symbols.append(ce_option)
                        paired_symbols.append(pe_option)

                    valid_pairs += 1
                    logger.info(f"Valid price-paired group for {base}: {len(valid_pairs_for_group)} pairs found")

                    # Log any unpaired options
                    unpaired_ce = len(ce_options) - len(valid_pairs_for_group)
                    unpaired_pe = len(pe_options) - len(valid_pairs_for_group)
                    if unpaired_ce > 0 or unpaired_pe > 0:
                        logger.info(f"Unpaired options in {base}: {unpaired_ce} CE, {unpaired_pe} PE")
                else:
                    # Group has both CE and PE but no valid price pairs
                    price_filtered_count += 1
                    logger.debug(f"Price filtered group {base}: {len(ce_options)} CE, {len(pe_options)} PE options (no valid price pairs)")
            else:
                # Invalid pair - missing CE or PE
                discarded_groups += 1
                logger.debug(f"Discarded group {base}: {len(ce_options)} CE, {len(pe_options)} PE options (missing CE or PE)")

        logger.info(f"CE/PE Pairing Results:")
        logger.info(f"  Total base groups processed: {len(base_groups)}")
        logger.info(f"  Valid paired groups: {valid_pairs}")
        logger.info(f"  Discarded groups (missing CE/PE): {discarded_groups}")
        logger.info(f"  Price filtered groups: {price_filtered_count}")
        logger.info(f"  Symbols before pairing: {len(filtered_symbols)}")
        logger.info(f"  Symbols after pairing: {len(paired_symbols)}")

        return paired_symbols
    
    def apply_filters(self, market_data: Dict[str, MarketData]) -> List[FilteredSymbol]:
        """
        Apply volume, LTP, CE/PE pairing, and MAE filters to market data.
        Filters are applied in sequence: Volume -> LTP -> CE/PE Pairing (if enabled) -> MAE (if enabled).

        Args:
            market_data: Dictionary of symbol to MarketData

        Returns:
            List of FilteredSymbol objects that pass all filters
        """
        # Step 1: Apply Volume and LTP filters first
        initial_filtered_symbols = []

        min_volume = self.config.min_volume
        max_volume = self.config.max_volume
        min_ltp = self.config.min_ltp_price
        max_ltp = self.config.max_ltp_price

        logger.info(f"Applying filters - Volume Range: {min_volume}-{max_volume}, LTP Range: {min_ltp}-{max_ltp}")
        logger.info(f"CE/PE Pairing: {'Enabled' if self.config.ce_pe_pairing_enabled else 'Disabled'}")
        logger.info(f"MAE Analysis: {'Enabled' if self.config.mae_enabled else 'Disabled'}")
        if self.config.mae_enabled:
            logger.info(f"MAE Length: {self.config.mae_length}, Source: {self.config.mae_source}, Offset: {self.config.mae_offset}, Smoothing: {self.config.mae_smoothing_line}({self.config.mae_smoothing_length})")
            logger.info(f"Timeframe - Interval: {self.config.timeframe_interval}, Days: {self.config.days_to_fetch}")

        # Track first symbol of each underlying+month combination for logging
        logged_combinations = set()

        # Track filtering statistics
        volume_filtered = 0
        ltp_filtered = 0
        total_processed = 0

        # Performance optimization: Pre-compile regex patterns for symbol parsing
        symbol_pattern = re.compile(r'^NSE:([A-Z&]+)(\d{2})([A-Z]{3})(\d+(?:\.\d+)?)(CE|PE)$')

        # Apply Volume and LTP filters first - optimized for large datasets
        for symbol, data in market_data.items():
            try:
                total_processed += 1

                # Step 1: Apply volume filter (both min and max) - fast numeric comparison
                if not (min_volume <= data.volume <= max_volume):
                    continue
                volume_filtered += 1

                # Step 2: Apply LTP filter - fast numeric comparison
                if not (min_ltp <= data.ltp <= max_ltp):
                    continue
                ltp_filtered += 1

                # Optimized symbol parsing using pre-compiled regex
                match = symbol_pattern.match(symbol)
                if not match:
                    logger.debug(f"Could not parse symbol: {symbol}")
                    continue

                underlying = match.group(1)
                year = match.group(2)
                month = match.group(3)
                strike_price = float(match.group(4))
                option_type = match.group(5)

                # Format expiry date
                expiry_date = f"20{year}-{month}"

                # Optimized logging: Only log first symbol per combination
                combination_key = f"{underlying}_{month}"
                if combination_key not in logged_combinations:
                    logger.info(f"First symbol for {underlying} {month}: {symbol} (LTP: {data.ltp}, Volume: {data.volume})")
                    logged_combinations.add(combination_key)

                # Create FilteredSymbol object - optimized construction
                filtered_symbol = FilteredSymbol(
                    strike=strike_price,
                    expiry_date=expiry_date,
                    option_type=option_type,
                    symbol=symbol,
                    ltp=data.ltp,
                    volume=data.volume,
                    open_price=data.open_price,
                    high=data.high,
                    low=data.low,
                    close=data.close,
                    prev_close=data.prev_close,
                    change=data.change,
                    change_percent=data.change_percent,
                    ema_short=None,
                    ema_long=None,
                    ema_signal=None,
                    pair_id=None,
                    pair_partner=None
                )
                initial_filtered_symbols.append(filtered_symbol)

            except Exception as e:
                logger.debug(f"Error processing symbol {symbol}: {e}")  # Reduced to debug level
                continue

        # Log initial filtering statistics
        logger.info(f"Initial Filtering Results:")
        logger.info(f"  Total symbols processed: {total_processed}")
        logger.info(f"  Passed volume filter: {volume_filtered}")
        logger.info(f"  Passed LTP filter: {ltp_filtered}")

        # Step 3: Apply CE/PE pairing filter if enabled
        ce_pe_filtered_symbols = self.apply_ce_pe_pairing_filter(initial_filtered_symbols)

        # Step 4: Apply MAE filter if enabled, preserving CE/PE pairs
        final_filtered_symbols = []
        mae_filtered = 0
        if self.config.mae_enabled:
            # Fetch all historical data in parallel (rate limit aware)
            historical_data_map = self.fetch_historical_data_parallel(
                ce_pe_filtered_symbols,
                self.config.timeframe_interval,
                self.config.days_to_fetch,
                max_workers=5  # You can make this configurable
            )

            # Group symbols by pair_id to preserve pairs during MAE filtering
            pair_groups = {}
            unpaired_symbols = []

            for symbol in ce_pe_filtered_symbols:
                if symbol.pair_id:
                    if symbol.pair_id not in pair_groups:
                        pair_groups[symbol.pair_id] = []
                    pair_groups[symbol.pair_id].append(symbol)
                else:
                    unpaired_symbols.append(symbol)

            # Process paired symbols - both must pass MAE or both are rejected
            for pair_id, pair_symbols in pair_groups.items():
                if len(pair_symbols) != 2:
                    logger.warning(f"Invalid pair {pair_id}: contains {len(pair_symbols)} symbols instead of 2")
                    continue

                pair_mae_results = []
                for symbol in pair_symbols:
                    try:
                        mae_value = None
                        mae_passed = True

                        # Use pre-fetched historical data
                        historical_data = historical_data_map.get(symbol.symbol, [])
                        if historical_data and len(historical_data) >= self.config.mae_length:
                            # Calculate MAE analysis
                            mae_default, mae_smoothed = self.mae_analyzer.calculate_mae(historical_data)
                            use_smoothed = getattr(self.config, 'mae_smoothing_enabled', False)
                            mae_value = mae_smoothed.iloc[-1] if use_smoothed else mae_default.iloc[-1]
                            # Check if current price is passing through MAE
                            mae_passed = self.mae_analyzer.is_price_passing_through_mae(
                                ohlc_data=historical_data,
                                current_price=symbol.ltp,
                                use_smoothed=use_smoothed
                            )
                        else:
                            logger.warning(f"Insufficient historical data for MAE calculation: {symbol.symbol}")
                            mae_passed = False

                        pair_mae_results.append((symbol, mae_value, mae_passed))

                    except Exception as e:
                        logger.warning(f"Error processing symbol {symbol.symbol} for MAE: {e}")
                        pair_mae_results.append((symbol, None, False))

                # Both symbols in pair must pass MAE for the pair to be included
                if len(pair_mae_results) == 2 and all(result[2] for result in pair_mae_results):
                    for symbol, mae_value, _ in pair_mae_results:
                        symbol.ema_short = mae_value
                        final_filtered_symbols.append(symbol)
                        mae_filtered += 1
                    logger.debug(f"Pair {pair_id} passed MAE filter")
                else:
                    failed_symbols = [result[0].symbol for result in pair_mae_results if not result[2]]
                    logger.debug(f"Pair {pair_id} filtered out by MAE - failed symbols: {failed_symbols}")

            # Process unpaired symbols individually
            for symbol in unpaired_symbols:
                try:
                    mae_value = None
                    mae_passed = True

                    # Use pre-fetched historical data
                    historical_data = historical_data_map.get(symbol.symbol, [])
                    if historical_data and len(historical_data) >= self.config.mae_length:
                        # Calculate MAE analysis
                        mae_default, mae_smoothed = self.mae_analyzer.calculate_mae(historical_data)
                        use_smoothed = getattr(self.config, 'mae_smoothing_enabled', False)
                        mae_value = mae_smoothed.iloc[-1] if use_smoothed else mae_default.iloc[-1]
                        # Check if current price is passing through MAE
                        mae_passed = self.mae_analyzer.is_price_passing_through_mae(
                            ohlc_data=historical_data,
                            current_price=symbol.ltp,
                            use_smoothed=use_smoothed
                        )
                        if not mae_passed:
                            logger.debug(f"Symbol {symbol.symbol} filtered out by MAE - LTP: {symbol.ltp}, MAE: {mae_value}")
                            continue
                    else:
                        logger.warning(f"Insufficient historical data for MAE calculation: {symbol.symbol}")
                        continue

                    # Update the filtered symbol with MAE value
                    mae_filtered += 1
                    symbol.ema_short = mae_value
                    final_filtered_symbols.append(symbol)

                except Exception as e:
                    logger.warning(f"Error processing symbol {symbol.symbol} for MAE: {e}")
                    continue
        else:
            # If MAE is disabled, pass through all CE/PE filtered symbols
            final_filtered_symbols = ce_pe_filtered_symbols

        # Log final filtering statistics
        logger.info(f"Final Filtering Results:")
        logger.info(f"  Symbols after CE/PE pairing: {len(ce_pe_filtered_symbols)}")
        if self.config.mae_enabled:
            logger.info(f"  Passed MAE filter: {mae_filtered}")
        logger.info(f"  Final symbols after all filters: {len(final_filtered_symbols)}")
        logger.info(f"  Overall success rate: {(len(final_filtered_symbols)/total_processed*100):.2f}%" if total_processed > 0 else "  Overall success rate: 0%")

        return final_filtered_symbols
    
    def scan_symbols(self) -> List[FilteredSymbol]:
        """
        Main scanning method that orchestrates the entire process.
        
        Returns:
            List of FilteredSymbol objects that pass all filters
        """
        try:
            logger.info("Starting index scanner...")
            
            # Step 1: Authenticate with Fyers
            if not self.authenticate_fyers():
                logger.error("Failed to authenticate with Fyers API")
                return []

            # Step 2: Download the latest symbols
            subprocess.run(["python", "symbol_downloader.py"], check=True)
            
            # Step 3: Get symbols for scanning
            logger.info("Loading symbols from CSV...")
            # When using 'ALL', pass None to use all target symbols from parser
            underlying_filter = None if 'ALL' in self.config.symbols else self.config.symbols
            symbols_to_scan = self.symbol_parser.get_symbols_for_scanning(
                underlying_symbols=underlying_filter
            )

            if not symbols_to_scan:
                logger.warning("No symbols found for scanning")
                return []

            # Save filtered symbols to CSV to avoid rate limiting
            symbol_objects = self.symbol_parser.get_symbol_details(underlying_symbols=underlying_filter)
            self.symbol_parser.save_filtered_symbols_to_csv(symbol_objects, "filtered_symbols.csv")

            logger.info(f"Found {len(symbols_to_scan)} symbols to scan")
            
            # Step 4: Fetch market data
            logger.info("Fetching market data from Fyers API...")
            market_data = self.fyers_client.get_quotes(symbols_to_scan)
            
            if not market_data:
                logger.warning("No market data received")
                return []
            
            logger.info(f"Received market data for {len(market_data)} symbols")

            # Write all market data to CSV file
            with open('all_market_data.csv', 'w', newline='') as csvfile:
                # Conditionally add mae_value column to the CSV only if MAE indicator is enabled.
                if self.config.mae_enabled:
                    fieldnames = ['symbol', 'ltp', 'volume', 'open_price', 'high', 'low', 'close', 'prev_close', 'change', 'change_percent', 'mae_value']
                else:
                    fieldnames = ['symbol', 'ltp', 'volume', 'open_price', 'high', 'low', 'close', 'prev_close', 'change', 'change_percent']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                for symbol, data in market_data.items():
                    row = {
                        'symbol': symbol,
                        'ltp': getattr(data, 'ltp', ''),
                        'volume': getattr(data, 'volume', ''),
                        'open_price': getattr(data, 'open_price', ''),
                        'high': getattr(data, 'high', ''),
                        'low': getattr(data, 'low', ''),
                        'close': getattr(data, 'close', ''),
                        'prev_close': getattr(data, 'prev_close', ''),
                        'change': getattr(data, 'change', ''),
                        'change_percent': getattr(data, 'change_percent', '')
                    }
                    if self.config.mae_enabled:
                        # Try to get the MAE value from the correct attribute (ema_short may not be set in market_data)
                        if hasattr(data, 'mae_value'):
                            row['mae_value'] = getattr(data, 'mae_value', '')
                        elif hasattr(data, 'ema_short'):
                            row['mae_value'] = getattr(data, 'ema_short', '')
                        else:
                            row['mae_value'] = ''
                    writer.writerow(row)

            # Step 5: Apply filters
            logger.info("Applying filters...")
            filtered_symbols = self.apply_filters(market_data)
            
            logger.info(f"Scanning complete. Found {len(filtered_symbols)} symbols matching criteria")
            return filtered_symbols
            
        except Exception as e:
            logger.error(f"Error during scanning: {e}")
            return []
    
    def get_scan_summary(self, filtered_symbols: List[FilteredSymbol]) -> Dict[str, int]:
        """
        Get summary statistics of the scan results.
        
        Args:
            filtered_symbols: List of filtered symbols
            
        Returns:
            Dictionary with summary statistics
        """
        summary = {
            'total_symbols': len(filtered_symbols),
            'nifty_symbols': 0,
            'banknifty_symbols': 0,
            'ce_options': 0,
            'pe_options': 0
        }
        
        for symbol in filtered_symbols:
            if 'NIFTY' in symbol.symbol and 'BANKNIFTY' not in symbol.symbol:
                summary['nifty_symbols'] += 1
            elif 'BANKNIFTY' in symbol.symbol:
                summary['banknifty_symbols'] += 1
                
            if symbol.option_type == 'CE':
                summary['ce_options'] += 1
            elif symbol.option_type == 'PE':
                summary['pe_options'] += 1
        
        return summary

    def fetch_historical_data_parallel(self, symbols: List[FilteredSymbol], interval: int, days_to_fetch: int, max_workers: int = 5) -> Dict[str, List]:
        """
        Fetch historical data for a list of symbols in parallel, respecting rate limits.
        Returns a dict mapping symbol to OHLC data list.
        """
        results = {}
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_symbol = {
                executor.submit(
                    self.fyers_client.get_historical_data,
                    symbol.symbol,
                    interval,
                    days_to_fetch
                ): symbol.symbol for symbol in symbols
            }
            for future in as_completed(future_to_symbol):
                symbol = future_to_symbol[future]
                try:
                    data = future.result()
                    results[symbol] = data
                except Exception as exc:
                    logger.warning(f"Error fetching historical data for {symbol}: {exc}")
                    results[symbol] = []
        return results
