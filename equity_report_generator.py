"""
Report generator for creating CSV reports of filtered equity symbols.
Generates reports with specified columns and formatting for equity symbols.
"""

import csv
import os
import logging
import re
from datetime import datetime
from typing import List, Dict, Any
from pathlib import Path

from equity_scanner import FilteredEquitySymbol

logger = logging.getLogger(__name__)

class EquityReportGenerator:
    """Generator for creating CSV reports of equity scan results."""
    
    def __init__(self, output_dir: str = "reports", config=None):
        """
        Initialize the equity report generator.
        
        Args:
            output_dir: Directory to save reports
            config: ConfigLoader object (optional, required for EMA logic)
        """
        self.output_dir = output_dir
        self.ensure_output_directory()
        self.config = config
        
        # Get configured symbols for dynamic pattern matching
        if config and hasattr(config, 'symbols'):
            self.target_symbols = set(config.symbols)
        else:
            # Fallback to empty set for equity symbols
            self.target_symbols = set()
        
    def ensure_output_directory(self) -> None:
        """Ensure the output directory exists."""
        try:
            Path(self.output_dir).mkdir(parents=True, exist_ok=True)
            logger.info(f"Output directory ready: {self.output_dir}")
        except Exception as e:
            logger.error(f"Failed to create output directory {self.output_dir}: {e}")
            raise

    def extract_underlying_from_symbol(self, symbol_str: str) -> str:
        """
        Extract underlying symbol from equity symbol string.

        Args:
            symbol_str: Symbol string like 'NSE:ABAN-EQ'

        Returns:
            Underlying symbol (e.g., 'ABAN', 'RELIANCE') or 'UNKNOWN'
        """
        try:
            # Remove NSE: prefix if present
            clean_symbol = symbol_str.replace('NSE:', '')

            # Extract underlying by removing -EQ suffix
            if clean_symbol.endswith('-EQ'):
                return clean_symbol[:-3]  # Remove last 3 characters (-EQ)
            else:
                return "UNKNOWN"

        except Exception as e:
            logger.debug(f"Error extracting underlying from {symbol_str}: {e}")
            return "UNKNOWN"

    def generate_csv_report(self, filtered_symbols: List[FilteredEquitySymbol], 
                          filename: str = None) -> str:
        """
        Generate CSV report of filtered equity symbols.
        
        Args:
            filtered_symbols: List of FilteredEquitySymbol objects
            filename: Optional custom filename
            
        Returns:
            Path to the generated CSV file
        """
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"equity_scan_{timestamp}.csv"
        
        filepath = os.path.join(self.output_dir, filename)
        
        try:
            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                # Define columns based on whether MAE is enabled
                if self.config and self.config.mae_enabled:
                    fieldnames = [
                        'Symbol', 'Underlying', 'LTP', 'Volume', 'Open', 'High', 'Low', 'Close',
                        'Prev Close', 'Change', 'Change %', 'EMA Short', 'EMA Long', 'EMA Signal'
                    ]
                else:
                    fieldnames = [
                        'Symbol', 'Underlying', 'LTP', 'Volume', 'Open', 'High', 'Low', 'Close',
                        'Prev Close', 'Change', 'Change %'
                    ]
                
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                
                for symbol in filtered_symbols:
                    row = {
                        'Symbol': symbol.symbol,
                        'Underlying': symbol.underlying,
                        'LTP': f"{symbol.ltp:.2f}",
                        'Volume': f"{symbol.volume:,}",
                        'Open': f"{symbol.open_price:.2f}",
                        'High': f"{symbol.high:.2f}",
                        'Low': f"{symbol.low:.2f}",
                        'Close': f"{symbol.close:.2f}",
                        'Prev Close': f"{symbol.prev_close:.2f}",
                        'Change': f"{symbol.change:.2f}",
                        'Change %': f"{symbol.change_percent:.2f}%"
                    }
                    
                    # Add MAE columns if enabled
                    if self.config and self.config.mae_enabled:
                        row.update({
                            'EMA Short': f"{symbol.ema_short:.4f}" if symbol.ema_short is not None else "N/A",
                            'EMA Long': f"{symbol.ema_long:.4f}" if symbol.ema_long is not None else "N/A",
                            'EMA Signal': symbol.ema_signal
                        })
                    
                    writer.writerow(row)
            
            logger.info(f"CSV report generated: {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"Error generating CSV report: {e}")
            raise

    def generate_summary_report(self, filtered_symbols: List[FilteredEquitySymbol], 
                              summary_stats: Dict[str, Any], filename: str = None) -> str:
        """
        Generate text summary report of scan results.
        
        Args:
            filtered_symbols: List of FilteredEquitySymbol objects
            summary_stats: Dictionary with summary statistics
            filename: Optional custom filename
            
        Returns:
            Path to the generated summary file
        """
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"equity_scan_summary_{timestamp}.txt"
        
        filepath = os.path.join(self.output_dir, filename)
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                # Header
                f.write("=" * 80 + "\n")
                f.write("EQUITY SCANNER SUMMARY REPORT\n")
                f.write("=" * 80 + "\n")
                f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                
                # Overall Statistics
                f.write("OVERALL STATISTICS:\n")
                f.write("-" * 40 + "\n")
                f.write(f"Total Symbols Found: {summary_stats.get('total_symbols', 0)}\n")
                f.write(f"Unique Underlyings: {summary_stats.get('unique_underlyings', 0)}\n")
                f.write(f"Average Volume: {summary_stats.get('avg_volume', 0):,}\n")
                f.write(f"Average LTP: ₹{summary_stats.get('avg_ltp', 0.0):.2f}\n\n")
                
                # Breakdown by Underlying
                if 'underlying_counts' in summary_stats:
                    f.write("BREAKDOWN BY UNDERLYING:\n")
                    f.write("-" * 40 + "\n")
                    underlying_counts = summary_stats['underlying_counts']
                    
                    # Sort by count (descending)
                    sorted_underlyings = sorted(underlying_counts.items(), 
                                              key=lambda x: x[1], reverse=True)
                    
                    for underlying, count in sorted_underlyings:
                        f.write(f"{underlying:<20}: {count:>5} symbols\n")
                    f.write("\n")
                
                # Configuration Used
                if self.config:
                    f.write("CONFIGURATION USED:\n")
                    f.write("-" * 40 + "\n")
                    f.write(f"Target Symbols: {', '.join(self.config.symbols)}\n")
                    f.write(f"Volume Filter: {self.config.min_volume:,} - {self.config.max_volume:,}\n")
                    f.write(f"LTP Filter: ₹{self.config.min_ltp_price:.2f} - ₹{self.config.max_ltp_price:.2f}\n")
                    
                    if self.config.mae_enabled:
                        f.write(f"MAE Indicator: Enabled (Length: {self.config.mae_length})\n")
                    else:
                        f.write("MAE Indicator: Disabled\n")
                    f.write("\n")
                
                # Top Performers by Volume
                if filtered_symbols:
                    f.write("TOP 10 BY VOLUME:\n")
                    f.write("-" * 40 + "\n")
                    top_by_volume = sorted(filtered_symbols, key=lambda x: x.volume, reverse=True)[:10]
                    
                    for i, symbol in enumerate(top_by_volume, 1):
                        f.write(f"{i:2d}. {symbol.underlying:<15} "
                               f"Vol: {symbol.volume:>10,} "
                               f"LTP: ₹{symbol.ltp:>8.2f}\n")
                    f.write("\n")
                
                # Top Performers by LTP
                if filtered_symbols:
                    f.write("TOP 10 BY LTP:\n")
                    f.write("-" * 40 + "\n")
                    top_by_ltp = sorted(filtered_symbols, key=lambda x: x.ltp, reverse=True)[:10]
                    
                    for i, symbol in enumerate(top_by_ltp, 1):
                        f.write(f"{i:2d}. {symbol.underlying:<15} "
                               f"LTP: ₹{symbol.ltp:>8.2f} "
                               f"Vol: {symbol.volume:>10,}\n")
                    f.write("\n")
                
                # MAE Analysis (if enabled)
                if self.config and self.config.mae_enabled and filtered_symbols:
                    mae_symbols = [s for s in filtered_symbols if s.ema_signal != 'NEUTRAL']
                    if mae_symbols:
                        f.write("MAE ANALYSIS:\n")
                        f.write("-" * 40 + "\n")
                        
                        # Count by signal
                        signal_counts = {}
                        for symbol in mae_symbols:
                            signal = symbol.ema_signal
                            signal_counts[signal] = signal_counts.get(signal, 0) + 1
                        
                        for signal, count in signal_counts.items():
                            f.write(f"{signal:<10}: {count:>5} symbols\n")
                        f.write("\n")
                
                f.write("=" * 80 + "\n")
                f.write("End of Report\n")
                f.write("=" * 80 + "\n")
            
            logger.info(f"Summary report generated: {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"Error generating summary report: {e}")
            raise

    def generate_full_report(self, filtered_symbols: List[FilteredEquitySymbol], 
                           summary_stats: Dict[str, Any]) -> Dict[str, str]:
        """
        Generate both CSV and summary reports.
        
        Args:
            filtered_symbols: List of FilteredEquitySymbol objects
            summary_stats: Dictionary with summary statistics
            
        Returns:
            Dictionary with paths to generated files
        """
        try:
            # Generate timestamp for consistent naming
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # Generate CSV report
            csv_filename = f"equity_scan_{timestamp}.csv"
            csv_path = self.generate_csv_report(filtered_symbols, csv_filename)
            
            # Generate summary report
            summary_filename = f"equity_scan_summary_{timestamp}.txt"
            summary_path = self.generate_summary_report(filtered_symbols, summary_stats, summary_filename)
            
            return {
                'csv_report': csv_path,
                'summary_report': summary_path
            }
            
        except Exception as e:
            logger.error(f"Error generating full report: {e}")
            raise