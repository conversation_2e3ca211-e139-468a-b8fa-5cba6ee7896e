"""
Market Type Scanner for Multi-Market Type Scanner.
Handles scanning of different market types (EQUITY, INDEX, FUTURES, OPTIONS).
"""

import logging
from typing import Dict, List, Optional, Any
from abc import ABC, abstractmethod
from dataclasses import dataclass

from config_loader import ConfigLoader
from universal_symbol_parser import UniversalSymbolParser, UniversalSymbol
from options_chain_filter import OptionsChainFilter
from fyers_client import FyersClient

logger = logging.getLogger(__name__)

@dataclass
class MarketData:
    """Market data for a symbol."""
    symbol: str
    ltp: float
    volume: int
    open_price: float = 0.0
    high_price: float = 0.0
    low_price: float = 0.0
    close_price: float = 0.0
    prev_close: float = 0.0
    change: float = 0.0
    change_percent: float = 0.0

@dataclass
class FilteredSymbol:
    """Filtered symbol with market data and metadata."""
    symbol: str
    underlying: str
    market_type: str
    market_data: MarketData
    
    # Market type specific fields
    suffix: Optional[str] = None
    expiry_year: Optional[str] = None
    expiry_month: Optional[str] = None
    strike_price: Optional[float] = None
    option_type: Optional[str] = None


class BaseMarketScanner(ABC):
    """Base class for market type scanners."""
    
    def __init__(self, config: ConfigLoader, market_type: str):
        """
        Initialize the base scanner.
        
        Args:
            config: Configuration loader instance
            market_type: Market type (EQUITY, INDEX, FUTURES, OPTIONS)
        """
        self.config = config
        self.market_type = market_type
        self.fyers_client = None
        
        # Initialize symbol parser
        self.symbol_parser = UniversalSymbolParser(config, config.symbols)
        
        logger.info(f"Initialized {market_type} scanner")
    
    def authenticate_fyers(self) -> bool:
        """Authenticate with Fyers API."""
        try:
            self.fyers_client = FyersClient(self.config)
            return self.fyers_client.authenticate()
        except Exception as e:
            logger.error(f"Failed to authenticate with Fyers: {e}")
            return False
    
    def get_symbols_for_scanning(self, underlying_symbols: Optional[List[str]] = None) -> List[str]:
        """Get symbols for scanning for this market type."""
        return self.symbol_parser.get_symbols_for_market_type(
            self.market_type, underlying_symbols
        )
    
    def fetch_market_data(self, symbols: List[str]) -> Dict[str, MarketData]:
        """Fetch market data for symbols."""
        if not self.fyers_client:
            logger.error("Fyers client not authenticated")
            return {}
        
        try:
            # Fetch data using Fyers client
            market_data = self.fyers_client.get_market_data(symbols)
            
            # Convert to MarketData objects
            converted_data = {}
            for symbol, data in market_data.items():
                converted_data[symbol] = MarketData(
                    symbol=symbol,
                    ltp=data.get('ltp', 0.0),
                    volume=data.get('volume', 0),
                    open_price=data.get('open', 0.0),
                    high_price=data.get('high', 0.0),
                    low_price=data.get('low', 0.0),
                    close_price=data.get('close', 0.0),
                    prev_close=data.get('prev_close', 0.0),
                    change=data.get('change', 0.0),
                    change_percent=data.get('change_percent', 0.0)
                )
            
            return converted_data
            
        except Exception as e:
            logger.error(f"Error fetching market data: {e}")
            return {}
    
    def apply_volume_filter(self, market_data: Dict[str, MarketData]) -> Dict[str, MarketData]:
        """Apply volume filter."""
        filtered_data = {}
        
        for symbol, data in market_data.items():
            if self.config.min_volume <= data.volume <= self.config.max_volume:
                filtered_data[symbol] = data
        
        logger.info(f"Volume filter: {len(filtered_data)}/{len(market_data)} symbols passed")
        return filtered_data
    
    def apply_ltp_filter(self, market_data: Dict[str, MarketData]) -> Dict[str, MarketData]:
        """Apply LTP filter."""
        filtered_data = {}
        
        for symbol, data in market_data.items():
            if self.config.min_ltp_price <= data.ltp <= self.config.max_ltp_price:
                filtered_data[symbol] = data
        
        logger.info(f"LTP filter: {len(filtered_data)}/{len(market_data)} symbols passed")
        return filtered_data
    
    @abstractmethod
    def apply_market_specific_filters(self, market_data: Dict[str, MarketData]) -> Dict[str, MarketData]:
        """Apply market type specific filters."""
        pass
    
    def convert_to_filtered_symbols(self, market_data: Dict[str, MarketData]) -> List[FilteredSymbol]:
        """Convert market data to FilteredSymbol objects."""
        filtered_symbols = []
        
        for symbol, data in market_data.items():
            # Parse symbol to get metadata
            csv_file = self.config.get_csv_file_for_market_type(self.market_type)
            parsed_symbol = self.symbol_parser.parse_symbol(symbol, csv_file)
            
            if parsed_symbol:
                filtered_symbol = FilteredSymbol(
                    symbol=symbol,
                    underlying=parsed_symbol.underlying,
                    market_type=self.market_type,
                    market_data=data,
                    suffix=parsed_symbol.suffix,
                    expiry_year=parsed_symbol.expiry_year,
                    expiry_month=parsed_symbol.expiry_month,
                    strike_price=parsed_symbol.strike_price,
                    option_type=parsed_symbol.option_type
                )
                filtered_symbols.append(filtered_symbol)
        
        return filtered_symbols
    
    def scan_symbols(self) -> List[FilteredSymbol]:
        """Main scanning method."""
        try:
            logger.info(f"Starting {self.market_type} scanning...")
            
            # Authenticate
            if not self.authenticate_fyers():
                logger.error("Authentication failed")
                return []
            
            # Get symbols
            underlying_filter = None if 'ALL' in self.config.symbols else self.config.symbols
            symbols_to_scan = self.get_symbols_for_scanning(underlying_filter)
            
            if not symbols_to_scan:
                logger.warning(f"No {self.market_type} symbols found for scanning")
                return []
            
            logger.info(f"Fetching market data for {len(symbols_to_scan)} {self.market_type} symbols")
            
            # Fetch market data
            market_data = self.fetch_market_data(symbols_to_scan)
            
            if not market_data:
                logger.warning("No market data received")
                return []
            
            # Apply filters
            filtered_data = self.apply_volume_filter(market_data)
            filtered_data = self.apply_ltp_filter(filtered_data)
            filtered_data = self.apply_market_specific_filters(filtered_data)
            
            # Convert to FilteredSymbol objects
            filtered_symbols = self.convert_to_filtered_symbols(filtered_data)
            
            logger.info(f"{self.market_type} scanning completed: {len(filtered_symbols)} symbols found")
            return filtered_symbols
            
        except Exception as e:
            logger.error(f"Error during {self.market_type} scanning: {e}")
            return []


class EquityScanner(BaseMarketScanner):
    """Scanner for equity symbols."""
    
    def __init__(self, config: ConfigLoader):
        super().__init__(config, 'EQUITY')
    
    def apply_market_specific_filters(self, market_data: Dict[str, MarketData]) -> Dict[str, MarketData]:
        """Apply equity-specific filters."""
        # No additional filters for equity beyond volume and LTP
        return market_data


class IndexScanner(BaseMarketScanner):
    """Scanner for index symbols."""
    
    def __init__(self, config: ConfigLoader):
        super().__init__(config, 'INDEX')
    
    def apply_market_specific_filters(self, market_data: Dict[str, MarketData]) -> Dict[str, MarketData]:
        """Apply index-specific filters."""
        # No additional filters for index beyond volume and LTP
        return market_data


class FuturesScanner(BaseMarketScanner):
    """Scanner for futures symbols."""
    
    def __init__(self, config: ConfigLoader):
        super().__init__(config, 'FUTURES')
    
    def apply_market_specific_filters(self, market_data: Dict[str, MarketData]) -> Dict[str, MarketData]:
        """Apply futures-specific filters."""
        # No additional filters for futures beyond volume and LTP
        return market_data


class OptionsScanner(BaseMarketScanner):
    """Scanner for options symbols."""
    
    def __init__(self, config: ConfigLoader):
        super().__init__(config, 'OPTIONS')
        self.options_filter = OptionsChainFilter(config)
    
    def apply_market_specific_filters(self, market_data: Dict[str, MarketData]) -> Dict[str, MarketData]:
        """Apply options-specific filters including CE/PE pairing."""
        # Convert market data to UniversalSymbol objects for options filtering
        symbols = []
        csv_file = self.config.get_csv_file_for_market_type('OPTIONS')
        
        for symbol in market_data.keys():
            parsed_symbol = self.symbol_parser.parse_symbol(symbol, csv_file)
            if parsed_symbol:
                symbols.append(parsed_symbol)
        
        # Apply options chain filtering
        filtered_symbols = self.options_filter.filter_options_symbols(symbols)
        
        # Convert back to market data dictionary
        filtered_data = {}
        filtered_symbol_names = {s.get_nse_symbol() for s in filtered_symbols}
        
        for symbol, data in market_data.items():
            if symbol in filtered_symbol_names:
                filtered_data[symbol] = data
        
        logger.info(f"Options chain filter: {len(filtered_data)}/{len(market_data)} symbols passed")
        return filtered_data


class MarketTypeScannerFactory:
    """Factory for creating market type scanners."""
    
    @staticmethod
    def create_scanner(market_type: str, config: ConfigLoader) -> BaseMarketScanner:
        """
        Create a scanner for the specified market type.
        
        Args:
            market_type: Market type (EQUITY, INDEX, FUTURES, OPTIONS)
            config: Configuration loader instance
            
        Returns:
            Appropriate scanner instance
        """
        if market_type == 'EQUITY':
            return EquityScanner(config)
        elif market_type == 'INDEX':
            return IndexScanner(config)
        elif market_type == 'FUTURES':
            return FuturesScanner(config)
        elif market_type == 'OPTIONS':
            return OptionsScanner(config)
        else:
            raise ValueError(f"Unknown market type: {market_type}")
    
    @staticmethod
    def get_supported_market_types() -> List[str]:
        """Get list of supported market types."""
        return ['EQUITY', 'INDEX', 'FUTURES', 'OPTIONS']
